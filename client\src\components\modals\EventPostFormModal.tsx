import {
	ActionIcon,
	Button,
	Divider,
	Group,
	Modal,
	ScrollArea,
	Stack,
	Text,
	Title,
	Tooltip,
	Image,
	SimpleGrid,
	Card,
} from "@mantine/core";
import CustomQuill from "../quill/CustomQuill";
import { useForm } from "@mantine/form";
import { useCallback, useRef, useState } from "react";
import { IconPhoto, IconX } from "@tabler/icons-react";
import { notifications } from "@mantine/notifications";
import { AnimatePresence, motion } from "framer-motion";
import unavailableImage from "@assets/unavailable-image.png";
import { GalleryViewer } from "../GalleryViewer";
import { useHover } from "@mantine/hooks";

type EventPostFormModalProps = {
	opened: boolean;
	onClose: () => void;
	postFormData?: any;
};

const ImageItem = ({
	url,
	index,
	onRemove,
	onPreview,
}: {
	url: string;
	index: number;
	onRemove: (index: number) => void;
	onPreview: (index: number) => void;
}) => {
	const { hovered, ref } = useHover();

	return (
		<motion.div
			ref={ref}
			layout
			initial={{ opacity: 0, scale: 1 }}
			animate={{
				opacity: 1,
				scale: 1,
				transition: {
					duration: 0.3,
					delay: 0.1 * index,
				},
			}}
			exit={{ opacity: 0, scale: 0.95 }}
		>
			<Card p={"0"} shadow="md" withBorder radius="md">
				<Image
					src={url}
					alt={`Preview ${index}`}
					fallbackSrc={unavailableImage}
					h="100%"
					w="100%"
					styles={{
						root: {
							aspectRatio: "185/100",
						},
					}}
					fit="cover"
					radius={"md"}
					style={{
						filter: hovered
							? "brightness(90%)"
							: "brightness(100%)",
						cursor: "pointer",
					}}
					onClick={() => onPreview(index)}
				/>

				<ActionIcon
					color="red"
					radius="xl"
					size="md"
					onClick={() => onRemove(index)}
					style={{
						position: "absolute",
						top: 4,
						right: 4,
						opacity: hovered ? 1 : 0,
						transition: "opacity 0.2s",
						pointerEvents: hovered ? "auto" : "none",
					}}
				>
					<IconX size={16} />
				</ActionIcon>
			</Card>
		</motion.div>
	);
};

const EventPostFormModal = (props: EventPostFormModalProps) => {
	const { opened, onClose, postFormData } = props;
	const [files, setFiles] = useState<File[]>([]);
	const [previewUrls, setPreviewUrls] = useState<string[]>([]);
	const fileInputRef = useRef<HTMLInputElement>(null);
	const [viewerOpened, setViewerOpened] = useState<number | null>(null);

	const handlePreview = useCallback((index: number) => {
		setViewerOpened(index);
	}, []);

	const form = useForm({
		initialValues: {
			content: postFormData?.content || "",
		},
	});

	const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
		const selectedFiles = event.target.files;
		if (selectedFiles) {
			if (files.length + selectedFiles.length > 25) {
				notifications.show({
					title: "File Limit Reached",
					message: "You can only upload a maximum of 25 files.",
					color: "red",
				});
				return;
			}

			const newFiles = Array.from(selectedFiles);
			setFiles([...files, ...newFiles]);
			const newPreviewUrls = newFiles.map(file =>
				URL.createObjectURL(file)
			);
			setPreviewUrls([...previewUrls, ...newPreviewUrls]);
		}
	};

	const handleRemoveFile = (index: number) => {
		const newFiles = [...files];
		newFiles.splice(index, 1);
		setFiles(newFiles);

		const newPreviewUrls = [...previewUrls];
		URL.revokeObjectURL(newPreviewUrls[index]);
		newPreviewUrls.splice(index, 1);
		setPreviewUrls(newPreviewUrls);

		if (fileInputRef.current) {
			fileInputRef.current.value = "";
		}
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		console.log(form.values, files);
	};

	return (
		<>
			<Modal
				opened={opened}
				onClose={onClose}
				title={
					<Stack gap={0}>
						<Title order={3}>
							{postFormData ? "Update Post" : "Create Post"}
						</Title>
						<Text c={"dimmed"} size="sm">
							Share your thoughts and experiences with the
							community
						</Text>
					</Stack>
				}
				size="xl"
				scrollAreaComponent={ScrollArea.Autosize}
				padding="lg"
				withCloseButton={false}
				trapFocus={false}
				closeOnEscape={false}
			>
				<form onSubmit={handleSubmit}>
					<Stack gap="lg">
						<CustomQuill
							value={form.values.content}
							customStyle={`
              .ql-container.ql-snow {
                border: 1px solid #ccc !important;
                border-top: none !important;
              }
              .ql-editor {
                padding: 12px 15px !important;
                max-height: 280px;
                overflow-y: auto;
              }
            `}
							minHeight="250px"
							onChange={val => form.setFieldValue("content", val)}
						/>

						{previewUrls.length > 0 && (
							<SimpleGrid cols={3}>
								<AnimatePresence mode="popLayout">
									{previewUrls.map((url, index) => (
										<ImageItem
											key={index}
											url={url}
											index={index}
											onRemove={handleRemoveFile}
											onPreview={handlePreview}
										/>
									))}
								</AnimatePresence>
							</SimpleGrid>
						)}

						<Tooltip label="Add Media">
							<ActionIcon
								onClick={() => fileInputRef.current?.click()}
								variant="subtle"
							>
								<IconPhoto size={32} />
							</ActionIcon>
						</Tooltip>
						<input
							accept="image/*"
							multiple
							onChange={handleFileChange}
							ref={fileInputRef}
							style={{ display: "none" }}
							type="file"
						/>
					</Stack>

					<Divider mt="xl" />

					<Group justify="flex-end" mt="lg">
						<Button onClick={onClose} variant="outline">
							Cancel
						</Button>
						<Button type="submit">Post</Button>
					</Group>
				</form>
			</Modal>

			{viewerOpened !== null && (
				<GalleryViewer
					opened={viewerOpened !== null}
					onClose={() => setViewerOpened(null)}
					images={previewUrls.map(media => media)}
					initial={viewerOpened || 0}
				/>
			)}
		</>
	);
};

export default EventPostFormModal;
