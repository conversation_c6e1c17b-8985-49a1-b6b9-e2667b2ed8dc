import { Router } from "express";
import { checkIsAdmin, checkLogin } from "../middleware/auth.js";
import {
	createEvent,
	deleteEvent,
	getAllEvents,
	getEventDetails,
	respondToEvent,
	updateEvent,
	publishEvent,
	getEventUsers,
} from "../controllers/event.controller.js";
import { getSignedUrlForUpload } from "../services/awsSpace.js";

const router = Router();

router.get("/signed-url/:eventId", async (req, res) => {
	try {
		const { fileName, fileType } = req.query;
		const { eventId } = req.params;

		if (!fileName || !fileType) {
			return res
				.status(400)
				.json({ message: "fileName and fileType are required" });
		}

		const S3Key = `SM360/events/${eventId}/${Date.now()}_${fileName}`;
		const uploadUrl = await getSignedUrlForUpload(S3Key, fileType);

		res.json({
			uploadUrl,
			S3Key,
		});
	} catch (err) {
		console.error("Signed URL error:", err);
		res.status(500).json({ message: "Failed to generate signed URL" });
	}
});

router.post("/create-event", checkLogin, checkIsAdmin, createEvent);

router.get("/get-events", checkLogin, getAllEvents);

router.get("/get-event/:eventId", checkLogin, getEventDetails);

router.put("/update-event/:eventId", checkLogin, checkIsAdmin, updateEvent);

router.delete("/delete-event/:eventId", checkLogin, checkIsAdmin, deleteEvent);

router.post("/attend/:eventId", checkLogin, respondToEvent);

router.put("/publish-event/:eventId", checkLogin, checkIsAdmin, publishEvent);

router.get("/:eventId/:type", checkLogin, getEventUsers);

export default router;
